<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Noda Bersih <PERSON>ry - Layanan <PERSON>tar-Jemput Gratis di <PERSON></title>
    <meta name="description" content="Laundry profesional dengan layanan antar-jemput gratis di Bekasi. Pakaian bersih, wangi premium, dan proses cepat. Pesan sekarang via WhatsApp!">
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#38BDF8',      // Biru Muda
                        secondary: '#2DD4BF',    // Hijau Tosca
                        textDark: '#374151',     // Abu-abu Gelap
                        bgClean: '#FFFFFF'       // Putih
                    },
                    fontFamily: {
                        'poppins': ['Poppins', 'sans-serif']
                    }
                }
            }
        }
    </script>
    
    <style type="text/tailwindcss">
        @layer base {
            body {
                @apply font-poppins bg-bgClean text-textDark
            }
        }
    </style>
</head>
<body class="loading">
     <!-- Navbar Section -->
<!-- Navbar Section -->
<nav class="sticky top-0 z-50 bg-white/90 backdrop-blur-sm shadow-sm transition-all duration-300">
    <div class="container mx-auto px-4 py-4">
        <div class="flex items-center justify-between">
            <!-- Logo/Brand -->
            <div class="flex items-center">
                <span class="text-primary font-bold text-xl">Noda Bersih Laundry</span>
            </div>

            <!-- Desktop Navigation -->
            <div class="hidden md:flex items-center space-x-8">
                <a href="#layanan" class="text-textDark hover:text-primary transition-colors duration-300">Layanan & Harga</a>
                <a href="#cara-pesan" class="text-textDark hover:text-primary transition-colors duration-300">Cara Pesan</a>
                <a href="#area" class="text-textDark hover:text-primary transition-colors duration-300">Area Kami</a>
                <a href="https://wa.me/6281234567890" 
                   class="bg-primary hover:bg-secondary text-white font-medium py-2 px-6 rounded-full transition-all duration-300 transform hover:scale-105 shadow-md hover:shadow-lg">
                    Order via WhatsApp
                </a>
            </div>

            <!-- Mobile Menu Button -->
            <div class="md:hidden">
                <button data-menu-button class="text-textDark focus:outline-none" aria-expanded="false" aria-label="Toggle menu">
                    <!-- Hamburger Icon -->
                    <svg class="w-6 h-6 block" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                    <!-- Close Icon (hidden by default) -->
                    <svg class="w-6 h-6 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>
    
    <!-- Mobile Menu Dropdown -->
    <div data-mobile-menu class="md:hidden hidden absolute top-full left-0 w-full bg-white shadow-lg transition-all duration-300">
        <div class="container mx-auto px-4 py-4 space-y-4">
            <a href="#layanan" class="block py-2 text-textDark hover:text-primary transition-colors duration-300">Layanan & Harga</a>
            <a href="#cara-pesan" class="block py-2 text-textDark hover:text-primary transition-colors duration-300">Cara Pesan</a>
            <a href="#area" class="block py-2 text-textDark hover:text-primary transition-colors duration-300">Area Kami</a>
            <a href="https://wa.me/6281234567890" 
               class="block w-full bg-primary hover:bg-secondary text-white font-medium py-3 px-6 rounded-lg text-center transition-all duration-300 shadow-md">
                Order via WhatsApp
            </a>
        </div>
    </div>
</nav>

<!-- Hero Section -->
<section class="py-12 md:py-20 bg-white" id="top">
    <div class="container mx-auto px-4"  data-animate>
        <div class="flex flex-col md:flex-row items-center gap-8 md:gap-12">
            
            <!-- Text Content - Left Side -->
            <div class="md:w-1/2 space-y-6">
                <h1 class="text-3xl md:text-4xl lg:text-5xl font-bold text-textDark leading-tight">
                    Laundry <span class="text-primary">Bersih</span> & <span class="text-secondary">Wangi Premium</span> di Bekasi
                </h1>
                
                <p class="text-lg md:text-xl text-textDark/80 leading-relaxed">
                    Layanan antar-jemput <span class="font-semibold text-primary">GRATIS</span>, proses cepat, hasil maksimal. 
                    Hemat waktu Anda sekarang!
                </p>
                
                <!-- CTA Buttons -->
                <div class="flex flex-col sm:flex-row gap-4 pt-4">
                    <a href="https://wa.me/6281234567890" 
                       class="bg-primary hover:bg-secondary text-white font-semibold py-3 px-8 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl text-center">
                        Order via WhatsApp Sekarang
                    </a>
                    <a href="#layanan" 
                       class="border-2 border-textDark/20 hover:border-primary text-textDark font-semibold py-3 px-8 rounded-full transition-all duration-300 text-center">
                        Lihat Layanan Kami
                    </a>
                </div>
                
                <!-- Key Benefits Cards -->
                <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 pt-8">
                    <div class="flex items-center space-x-2">
                        <span class="text-primary text-xl">✓</span>
                        <span class="text-sm font-medium">Antar-Jemput Gratis</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="text-primary text-xl">✓</span>
                        <span class="text-sm font-medium">Garansi Bersih & Wangi</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="text-primary text-xl">✓</span>
                        <span class="text-sm font-medium">Proses Cepat 24-48 Jam</span>
                    </div>
                </div>
            </div>
            
            <!-- Image/Illustration - Right Side -->
            <div class="md:w-1/2 flex justify-center">
                <div class="relative">
                    <div class="w-64 h-64 md:w-80 md:h-80 bg-gradient-to-br from-primary/10 to-secondary/10 rounded-full flex items-center justify-center">
                        <div class="w-48 h-48 md:w-60 md:h-60 rounded-full overflow-hidden shadow-lg">
                            <img src="https://images.pexels.com/photos/4700411/pexels-photo-4700411.jpeg" 
                                 alt="Noda Bersih Laundry - Pakaian Bersih & Wangi" 
                                 class="w-full h-full object-cover"
                                 loading="lazy">
                        </div>
                    </div>
                    <!-- Decorative Elements -->
                    <div class="absolute -top-4 -right-4 w-16 h-16 bg-primary/20 rounded-full"></div>
                    <div class="absolute -bottom-4 -left-4 w-12 h-12 bg-secondary/20 rounded-full"></div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Keunggulan Section -->
<section class="py-16 bg-gray-50" data-animate>
    <div class="container mx-auto px-4">
        <!-- Section Header -->
        <div class="text-center max-w-3xl mx-auto mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-textDark mb-4">
                Mengapa Memilih <span class="text-primary">Noda Bersih Laundry</span>?
            </h2>
            <p class="text-lg text-textDark/70">
                Kami berkomitmen memberikan layanan terbaik untuk kenyamanan Anda
            </p>
        </div>

        <!-- Keunggulan Cards Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            
            <!-- Keunggulan 1 -->
            <div class="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-all duration-300 transform hover:-translate-y-1">
                <div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                    <svg class="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-textDark mb-2">Layanan Antar-Jemput Gratis</h3>
                <p class="text-textDark/70">Kami jemput dan antar pakaian Anda secara gratis di area Bekasi</p>
            </div>

            <!-- Keunggulan 2 -->
            <div class="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-all duration-300 transform hover:-translate-y-1">
                <div class="w-16 h-16 bg-secondary/10 rounded-full flex items-center justify-center mb-4">
                    <svg class="w-8 h-8 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-textDark mb-2">Garansi Pakaian Bersih & Wangi Premium</h3>
                <p class="text-textDark/70">Setiap cucian diperiksa kembali dan diberi pewangi tahan lama</p>
            </div>

            <!-- Keunggulan 3 -->
            <div class="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-all duration-300 transform hover:-translate-y-1">
                <div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                    <svg class="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-textDark mb-2">Proses Cepat & Tepat Waktu</h3>
                <p class="text-textDark/70">Laundry selesai dalam 24-48 jam dengan kualitas terjaga</p>
            </div>

            <!-- Keunggulan 4 -->
            <div class="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-all duration-300 transform hover:-translate-y-1">
                <div class="w-16 h-16 bg-secondary/10 rounded-full flex items-center justify-center mb-4">
                    <svg class="w-8 h-8 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 18l-4-4m0 0l4-4m-4 4h16"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-textDark mb-2">Pemesanan Mudah via WhatsApp</h3>
                <p class="text-textDark/70">Hanya dengan satu klik, layanan laundry langsung sampai ke rumah Anda</p>
            </div>

        </div>
    </div>
</section>

<!-- Cara Pesan Section -->
<section class="py-16 bg-white" data-animate id="cara-pesan">
    <div class="container mx-auto px-4">
        <!-- Section Header -->
        <div class="text-center max-w-3xl mx-auto mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-textDark mb-4">
                Cara Pemesanan yang <span class="text-primary">Mudah</span> & <span class="text-secondary">Praktis</span>
            </h2>
            <p class="text-lg text-textDark/70">
                Hanya dalam 4 langkah sederhana, laundry Anda sudah kami proses
            </p>
        </div>

        <!-- Steps Container -->
        <div class="relative">
            <!-- Connector Line - Hidden on mobile, visible on desktop -->
            <div class="hidden md:block absolute top-12 left-1/4 right-1/4 h-0.5 bg-gray-200 z-0"></div>
            
            <!-- Steps Grid -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8 relative z-10">
                
                <!-- Step 1 -->
                <div class="text-center">
                    <div class="relative mb-6">
                        <div class="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto shadow-lg">
                            <span class="text-white text-2xl font-bold">1</span>
                        </div>
                        <!-- Mobile Connector Line -->
                        <div class="md:hidden absolute top-16 left-1/2 w-0.5 h-8 bg-gray-200 transform -translate-x-1/2"></div>
                    </div>
                    <div class="bg-gray-50 rounded-xl p-6 hover:shadow-md transition-shadow duration-300">
                        <div class="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold text-textDark mb-3">WhatsApp Kami</h3>
                        <p class="text-textDark/70 text-sm">Klik tombol WhatsApp dan beri tahu kami jenis & jumlah pakaian</p>
                    </div>
                </div>

                <!-- Step 2 -->
                <div class="text-center">
                    <div class="relative mb-6">
                        <div class="w-16 h-16 bg-secondary rounded-full flex items-center justify-center mx-auto shadow-lg">
                            <span class="text-white text-2xl font-bold">2</span>
                        </div>
                        <!-- Mobile Connector Line -->
                        <div class="md:hidden absolute top-16 left-1/2 w-0.5 h-8 bg-gray-200 transform -translate-x-1/2"></div>
                    </div>
                    <div class="bg-gray-50 rounded-xl p-6 hover:shadow-md transition-shadow duration-300">
                        <div class="w-12 h-12 bg-secondary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-6 h-6 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold text-textDark mb-3">Jadwalkan Penjemputan</h3>
                        <p class="text-textDark/70 text-sm">Tentukan waktu jemput yang sesuai dengan jadwal Anda</p>
                    </div>
                </div>

                <!-- Step 3 -->
                <div class="text-center">
                    <div class="relative mb-6">
                        <div class="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto shadow-lg">
                            <span class="text-white text-2xl font-bold">3</span>
                        </div>
                        <!-- Mobile Connector Line -->
                        <div class="md:hidden absolute top-16 left-1/2 w-0.5 h-8 bg-gray-200 transform -translate-x-1/2"></div>
                    </div>
                    <div class="bg-gray-50 rounded-xl p-6 hover:shadow-md transition-shadow duration-300">
                        <div class="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold text-textDark mb-3">Kami Jemput & Proses</h3>
                        <p class="text-textDark/70 text-sm">Tim kami menjemput, mencuci, dan mengemas dengan rapi</p>
                    </div>
                </div>

                <!-- Step 4 -->
                <div class="text-center">
                    <div class="relative mb-6">
                        <div class="w-16 h-16 bg-secondary rounded-full flex items-center justify-center mx-auto shadow-lg">
                            <span class="text-white text-2xl font-bold">4</span>
                        </div>
                    </div>
                    <div class="bg-gray-50 rounded-xl p-6 hover:shadow-md transition-shadow duration-300">
                        <div class="w-12 h-12 bg-secondary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-6 h-6 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold text-textDark mb-3">Pakaian Diantar Kembali</h3>
                        <p class="text-textDark/70 text-sm">Pakaian bersih & wangi dikembalikan ke rumah Anda</p>
                    </div>
                </div>

            </div>
        </div>

        <!-- CTA Button -->
        <div class="text-center mt-16">
            <a href="https://wa.me/6281234567890" 
               class="inline-block bg-primary hover:bg-secondary text-white font-semibold py-4 px-10 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                Pesan Sekarang via WhatsApp
            </a>
        </div>
    </div>
</section>

<!-- Layanan & Harga Section -->
<section class="py-16 bg-gray-50" id="layanan" data-animate>
    <div class="container mx-auto px-4">
        <!-- Section Header -->
        <div class="text-center max-w-3xl mx-auto mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-textDark mb-4">
                Layanan Laundry & <span class="text-primary">Harga Terjangkau</span>
            </h2>
            <p class="text-lg text-textDark/70">
                Pilihan paket laundry lengkap dengan harga bersaing di Bekasi
            </p>
        </div>

        <!-- Pricing Cards Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            
            <!-- Service Card 1 - Express -->
            <div class="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-all duration-300 transform hover:-translate-y-1 border border-gray-100">
                <div class="text-center mb-6">
                    <h3 class="text-xl font-bold text-textDark mb-2">Cuci Kilat</h3>
                    <div class="flex items-baseline justify-center">
                        <span class="text-3xl font-bold text-primary">Rp 12.000</span>
                        <span class="text-gray-500 ml-1">/kg</span>
                    </div>
                    <p class="text-sm text-gray-500 mt-1">24 Jam Selesai</p>
                </div>
                
                <ul class="space-y-3 mb-6">
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-primary mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <span class="text-sm">Cuci + Setrika</span>
                    </li>
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-primary mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <span class="text-sm">Antar-jemput Gratis</span>
                    </li>
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-primary mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <span class="text-sm">Pewangi Premium</span>
                    </li>
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-primary mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <span class="text-sm">Packing Rapi</span>
                    </li>
                </ul>
                
                <a href="https://wa.me/6281234567890?text=Halo,%20saya%20mau%20pesan%20layanan%20Cuci%20Kilat" 
                   class="block w-full bg-primary hover:bg-secondary text-white font-semibold py-3 px-4 rounded-lg text-center transition-all duration-300">
                    Pesan Sekarang
                </a>
            </div>

            <!-- Service Card 2 - Reguler (Best Value) -->
            <div class="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border-2 border-primary relative">
                <!-- Best Value Badge -->
                <div class="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span class="bg-primary text-white text-xs font-bold px-4 py-1 rounded-full">BEST VALUE</span>
                </div>
                
                <div class="text-center mb-6 pt-3">
                    <h3 class="text-xl font-bold text-textDark mb-2">Cuci Reguler</h3>
                    <div class="flex items-baseline justify-center">
                        <span class="text-3xl font-bold text-primary">Rp 8.000</span>
                        <span class="text-gray-500 ml-1">/kg</span>
                    </div>
                    <p class="text-sm text-gray-500 mt-1">48 Jam Selesai</p>
                </div>
                
                <ul class="space-y-3 mb-6">
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-primary mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <span class="text-sm">Cuci + Setrika</span>
                    </li>
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-primary mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <span class="text-sm">Antar-jemput Gratis</span>
                    </li>
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-primary mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <span class="text-sm">Pewangi Standar</span>
                    </li>
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-primary mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <span class="text-sm">Packing Rapi</span>
                    </li>
                </ul>
                
                <a href="https://wa.me/6281234567890?text=Halo,%20saya%20mau%20pesan%20layanan%20Cuci%20Reguler" 
                   class="block w-full bg-primary hover:bg-secondary text-white font-semibold py-3 px-4 rounded-lg text-center transition-all duration-300">
                    Pesan Sekarang
                </a>
            </div>

            <!-- Service Card 3 - Paket Hemat -->
            <div class="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-all duration-300 transform hover:-translate-y-1 border border-gray-100">
                <div class="text-center mb-6">
                    <h3 class="text-xl font-bold text-textDark mb-2">Paket Hemat</h3>
                    <div class="flex items-baseline justify-center">
                        <span class="text-3xl font-bold text-primary">Rp 6.000</span>
                        <span class="text-gray-500 ml-1">/kg</span>
                    </div>
                    <p class="text-sm text-gray-500 mt-1">72 Jam Selesai</p>
                </div>
                
                <ul class="space-y-3 mb-6">
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-primary mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <span class="text-sm">Cuci Saja</span>
                    </li>
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-primary mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <span class="text-sm">Antar-jemput Gratis</span>
                    </li>
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-primary mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <span class="text-sm">Pewangi Standar</span>
                    </li>
                    <li class="flex items-center text-gray-400">
                        <svg class="w-5 h-5 text-gray-300 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                        <span class="text-sm line-through">Setrika</span>
                    </li>
                </ul>
                
                <a href="https://wa.me/6281234567890?text=Halo,%20saya%20mau%20pesan%20layanan%20Paket%20Hemat" 
                   class="block w-full bg-primary hover:bg-secondary text-white font-semibold py-3 px-4 rounded-lg text-center transition-all duration-300">
                    Pesan Sekarang
                </a>
            </div>

            <!-- Service Card 4 - Satuan Premium -->
            <div class="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-all duration-300 transform hover:-translate-y-1 border border-gray-100">
                <div class="text-center mb-6">
                    <h3 class="text-xl font-bold text-textDark mb-2">Laundry Satuan</h3>
                    <div class="flex items-baseline justify-center">
                        <span class="text-3xl font-bold text-primary">Rp 15.000</span>
                        <span class="text-gray-500 ml-1">/pcs</span>
                    </div>
                    <p class="text-sm text-gray-500 mt-1">48 Jam Selesai</p>
                </div>
                
                <ul class="space-y-3 mb-6">
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-primary mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <span class="text-sm">Setrika + Lipat Rapi</span>
                    </li>
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-primary mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <span class="text-sm">Treatment Khusus</span>
                    </li>
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-primary mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <span class="text-sm">Packing Premium</span>
                    </li>
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-primary mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <span class="text-sm">Antar-jemput Gratis</span>
                    </li>
                </ul>
                
                <a href="https://wa.me/6281234567890?text=Halo,%20saya%20mau%20pesan%20layanan%20Laundry%20Satuan" 
                   class="block w-full bg-primary hover:bg-secondary text-white font-semibold py-3 px-4 rounded-lg text-center transition-all duration-300">
                    Pesan Sekarang
                </a>
            </div>

        </div>

        <!-- Pricing Notes -->
        <div class="mt-12 text-center">
            <p class="text-sm text-gray-500 italic">
                *Harga belum termasuk ongkos kirim untuk area jauh. 
                *Minimal order 3kg untuk layanan reguler.
            </p>
        </div>
    </div>
</section>

<!-- Area Jangkauan Layanan Section -->
<section class="py-16 bg-white" data-animate id="area">
    <div class="container mx-auto px-4">
        <!-- Section Header -->
        <div class="text-center max-w-3xl mx-auto mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-textDark mb-4">
                Area Jangkauan <span class="text-primary">Layanan Kami</span>
            </h2>
            <p class="text-lg text-textDark/70">
                Kami melayani antar-jemput laundry gratis di seluruh wilayah Bekasi dan sekitarnya
            </p>
        </div>

        <!-- Area Coverage Content - REVISED LAYOUT -->
        <div class="flex flex-col gap-10">
            
            <!-- Map Section - Full Width dengan Height yang Lebih Besar -->
            <div class="w-full">
                <div class="bg-gradient-to-br from-primary/5 to-secondary/5 rounded-2xl p-6 border border-gray-100">
                    <div class="w-full min-h-[300px] sm:min-h-[400px] md:min-h-[500px] bg-gray-100 rounded-xl flex items-center justify-center relative overflow-hidden">
                        <!-- Simplified Map Illustration dengan Ukuran Lebih Besar -->
                        <div class="absolute inset-0 p-6 sm:p-8">
                            <!-- Bekasi Map Outline (Lebih Besar) -->
                            <div class="w-full h-full border-2 border-primary/30 rounded-lg relative">
                                <!-- Covered Areas Markers - Ukuran Responsif -->
                                <div class="absolute top-[20%] left-[30%] w-3 sm:w-4 md:w-5 h-3 sm:h-4 md:h-5 bg-primary rounded-full animate-pulse"></div>
                                <div class="absolute top-[25%] right-[25%] w-3 sm:w-4 md:w-5 h-3 sm:h-4 md:h-5 bg-primary rounded-full animate-pulse"></div>
                                <div class="absolute bottom-[30%] left-[50%] w-3 sm:w-4 md:w-5 h-3 sm:h-4 md:h-5 bg-primary rounded-full animate-pulse"></div>
                                <div class="absolute top-[40%] left-[20%] w-3 sm:w-4 md:w-5 h-3 sm:h-4 md:h-5 bg-primary rounded-full animate-pulse"></div>
                                <div class="absolute bottom-[25%] right-[35%] w-3 sm:w-4 md:w-5 h-3 sm:h-4 md:h-5 bg-primary rounded-full animate-pulse"></div>
                                <div class="absolute top-[35%] left-[40%] w-3 sm:w-4 md:w-5 h-3 sm:h-4 md:h-5 bg-secondary rounded-full animate-pulse"></div>
                                <div class="absolute bottom-[40%] right-[20%] w-3 sm:w-4 md:w-5 h-3 sm:h-4 md:h-5 bg-secondary rounded-full animate-pulse"></div>
                            </div>
                            <!-- Legend -->
                            <div class="absolute bottom-4 left-4 sm:bottom-6 sm:left-6 bg-white px-3 py-2 sm:px-4 sm:py-3 rounded-lg shadow-sm">
                                <div class="flex items-center">
                                    <div class="w-3 h-3 sm:w-4 sm:h-4 bg-primary rounded-full mr-2"></div>
                                    <span class="text-xs sm:text-sm text-gray-600">Area Terlayani</span>
                                </div>
                            </div>
                        </div>
                        <span class="text-gray-400 font-medium text-base sm:text-lg">Peta Wilayah Bekasi</span>
                    </div>
                </div>
            </div>

            <!-- Info Section - Di Bawah Map untuk Semua Device -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Covered Areas -->
                <div>
                    <h3 class="text-2xl font-bold text-textDark mb-6 flex items-center">
                        <svg class="w-6 h-6 text-primary mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        Wilayah Terlayani
                    </h3>
                    
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                        <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                            <svg class="w-5 h-5 text-primary mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span class="font-medium">Bekasi Kota</span>
                        </div>
                        <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                            <svg class="w-5 h-5 text-primary mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span class="font-medium">Bekasi Timur</span>
                        </div>
                        <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                            <svg class="w-5 h-5 text-primary mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span class="font-medium">Bekasi Barat</span>
                        </div>
                        <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                            <svg class="w-5 h-5 text-primary mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span class="font-medium">Bekasi Utara</span>
                        </div>
                        <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                            <svg class="w-5 h-5 text-primary mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span class="font-medium">Bekasi Selatan</span>
                        </div>
                        <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                            <svg class="w-5 h-5 text-primary mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span class="font-medium">Rawalumbu</span>
                        </div>
                        <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                            <svg class="w-5 h-5 text-primary mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span class="font-medium">Medan Satria</span>
                        </div>
                        <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                            <svg class="w-5 h-5 text-primary mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span class="font-medium">Mustika Jaya</span>
                        </div>
                    </div>
                </div>

                <!-- Service Info -->
                <div>
                    <div class="bg-gradient-to-r from-primary/5 to-secondary/5 rounded-xl p-6 border border-gray-100 h-full">
                        <h4 class="text-lg font-bold text-textDark mb-4">Info Layanan Antar-Jemput</h4>
                        <ul class="space-y-3 text-sm text-gray-600">
                            <li class="flex items-start">
                                <svg class="w-4 h-4 text-primary mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span>Jadwal fleksibel sesuai kebutuhan Anda</span>
                            </li>
                            <li class="flex items-start">
                                <svg class="w-4 h-4 text-primary mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                                </svg>
                                <span>Gratis antar-jemput untuk order minimal 5kg</span>
                            </li>
                            <li class="flex items-start">
                                <svg class="w-4 h-4 text-primary mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                </svg>
                                <span>Hubungi kami untuk area di luar wilayah tercantum</span>
                            </li>
                        </ul>
                        
                        <!-- CTA Button -->
                        <div class="mt-6">
                            <a href="https://wa.me/6281234567890?text=Halo,%20saya%20mau%20cek%20area%20layanan" 
                               class="inline-flex items-center bg-primary hover:bg-secondary text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-md">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                </svg>
                                Cek Area Anda
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Testimoni Section - Static Grid Layout -->
<section class="py-16 bg-gray-50" data-animate id="testimoni">
    <div class="container mx-auto px-4">
        <!-- Section Header -->
        <div class="text-center max-w-3xl mx-auto mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-textDark mb-4">
                Apa Kata <span class="text-primary">Pelanggan Kami</span>
            </h2>
            <p class="text-lg text-textDark/70">
                Pengalaman nyata dari pelanggan yang puas dengan layanan kami
            </p>
        </div>

        <!-- Testimonial Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            
            <!-- Testimonial Card 1 -->
            <div class="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-all duration-300 transform hover:-translate-y-1">
                <div class="text-primary text-5xl opacity-20 mb-4">"</div>
                <p class="text-textDark/80 italic mb-6 leading-relaxed">
                    Pelayanan sangat memuaskan! Pakaian saya bersih dan wangi sekali. Antar-jemputnya juga tepat waktu.
                </p>
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-full flex items-center justify-center mr-4">
                        <span class="text-primary font-bold">SP</span>
                    </div>
                    <div>
                        <h4 class="font-semibold text-textDark">Sari P.</h4>
                        <p class="text-sm text-gray-500">Bekasi Timur</p>
                        <div class="flex mt-1">
                            <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Testimonial Card 2 -->
            <div class="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-all duration-300 transform hover:-translate-y-1">
                <div class="text-primary text-5xl opacity-20 mb-4">"</div>
                <p class="text-textDark/80 italic mb-6 leading-relaxed">
                    Hemat waktu banget! Tinggal WhatsApp, petugasnya langsung datang. Hasil laundry rapi dan cepat.
                </p>
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-full flex items-center justify-center mr-4">
                        <span class="text-primary font-bold">BK</span>
                    </div>
                    <div>
                        <h4 class="font-semibold text-textDark">Budi K.</h4>
                        <p class="text-sm text-gray-500">Bekasi Kota</p>
                        <div class="flex mt-1">
                            <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Testimonial Card 3 -->
            <div class="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-all duration-300 transform hover:-translate-y-1">
                <div class="text-primary text-5xl opacity-20 mb-4">"</div>
                <p class="text-textDark/80 italic mb-6 leading-relaxed">
                    Harga terjangkau dengan kualitas premium. Anak saya senang karena bajunya wangi terus!
                </p>
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-full flex items-center justify-center mr-4">
                        <span class="text-primary font-bold">DR</span>
                    </div>
                    <div>
                        <h4 class="font-semibold text-textDark">Dewi R.</h4>
                        <p class="text-sm text-gray-500">Rawalumbu</p>
                        <div class="flex mt-1">
                            <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Testimonial Card 4 -->
            <div class="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-all duration-300 transform hover:-translate-y-1">
                <div class="text-primary text-5xl opacity-20 mb-4">"</div>
                <p class="text-textDark/80 italic mb-6 leading-relaxed">
                    Pertama kali coba dan langsung jadi langganan. Prosesnya transparan dan ramah.
                </p>
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-full flex items-center justify-center mr-4">
                        <span class="text-primary font-bold">AM</span>
                    </div>
                    <div>
                        <h4 class="font-semibold text-textDark">Ahmad M.</h4>
                        <p class="text-sm text-gray-500">Medan Satria</p>
                        <div class="flex mt-1">
                            <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Testimonial Card 5 -->
            <div class="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-all duration-300 transform hover:-translate-y-1">
                <div class="text-primary text-5xl opacity-20 mb-4">"</div>
                <p class="text-textDark/80 italic mb-6 leading-relaxed">
                    Layanan antar-jemput gratisnya sangat membantu. Kualitas laundry juga sangat bagus!
                </p>
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-full flex items-center justify-center mr-4">
                        <span class="text-primary font-bold">RT</span>
                    </div>
                    <div>
                        <h4 class="font-semibold text-textDark">Rina T.</h4>
                        <p class="text-sm text-gray-500">Bekasi Selatan</p>
                        <div class="flex mt-1">
                            <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Testimonial Card 6 -->
            <div class="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-all duration-300 transform hover:-translate-y-1">
                <div class="text-primary text-5xl opacity-20 mb-4">"</div>
                <p class="text-textDark/80 italic mb-6 leading-relaxed">
                    Sangat direkomendasikan! Pelayanan profesional dan hasilnya memuaskan sekali.
                </p>
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-full flex items-center justify-center mr-4">
                        <span class="text-primary font-bold">JK</span>
                    </div>
                    <div>
                        <h4 class="font-semibold text-textDark">Joko K.</h4>
                        <p class="text-sm text-gray-500">Bekasi Utara</p>
                        <div class="flex mt-1">
                            <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-16 bg-gradient-to-r from-primary to-secondary"data-animate>
    <div class="container mx-auto px-4">
        <div class="flex flex-col lg:flex-row items-center gap-12">
            
            <!-- Text Content -->
            <div class="lg:w-1/2 text-white">
                <h2 class="text-3xl md:text-4xl font-bold mb-6">
                    Hemat Waktu & Tenaga Anda Sekarang!
                </h2>
                <p class="text-lg md:text-xl opacity-90 mb-8 leading-relaxed">
                    Laundry bersih, wangi premium, dan antar-jemput gratis. 
                    Pesan sekarang sebelum promo habis!
                </p>
                
                <!-- Stats or Social Proof (Optional) -->
                <div class="flex flex-wrap gap-6 mb-8">
                    <div class="flex items-center">
                        <svg class="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        <span>1000+ Pelanggan Puas</span>
                    </div>
                    <div class="flex items-center">
                        <svg class="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        <span>Gratis Antar-Jemput</span>
                    </div>
                </div>
            </div>
            
            <!-- CTA Buttons -->
            <div class="lg:w-1/2 flex flex-col sm:flex-row gap-4">
                <a href="https://wa.me/6281234567890?text=Halo,%20saya%20mau%20pesan%20laundry" 
                   class="flex-1 bg-white text-primary font-bold py-4 px-8 rounded-xl hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 shadow-lg text-center flex items-center justify-center">
                    <svg class="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.480-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.297-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413z"></path>
                    </svg>
                    Order via WhatsApp
                </a>
            </div>
            
        </div>
    </div>
</section>

<!-- Footer Section -->
<footer class="bg-gray-900 text-white pt-16 pb-8">
    <div class="container mx-auto px-4">
        
        <!-- Footer Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12 mb-12">
            
            <!-- Brand Info -->
            <div class="space-y-4">
                <h3 class="text-2xl font-bold text-primary">Noda Bersih Laundry</h3>
                <p class="text-gray-300 leading-relaxed">
                    Layanan laundry profesional dengan antar-jemput gratis di wilayah Bekasi dan sekitarnya.
                </p>
                <div class="flex space-x-4">
                    <a href="https://wa.me/6281234567890" class="w-10 h-10 bg-primary rounded-full flex items-center justify-center hover:bg-secondary transition-colors">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.480-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.297-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347"></path>
                        </svg>
                    </a>
                    <a href="#" class="w-10 h-10 bg-primary rounded-full flex items-center justify-center hover:bg-secondary transition-colors">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.162-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.097.118.112.222.085.343-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.164-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001.012.001z"></path>
                        </svg>
                    </a>
                </div>
            </div>
            
            <!-- Layanan -->
            <div>
                <h4 class="text-lg font-semibold mb-4 text-primary">Layanan</h4>
                <ul class="space-y-2">
                    <li><a href="#layanan" class="text-gray-300 hover:text-white transition-colors">Cuci Kilat</a></li>
                    <li><a href="#layanan" class="text-gray-300 hover:text-white transition-colors">Cuci Reguler</a></li>
                    <li><a href="#layanan" class="text-gray-300 hover:text-white transition-colors">Paket Hemat</a></li>
                    <li><a href="#layanan" class="text-gray-300 hover:text-white transition-colors">Laundry Satuan</a></li>
                    <li><a href="#area" class="text-gray-300 hover:text-white transition-colors">Area Layanan</a></li>
                </ul>
            </div>
            
            <!-- Kontak -->
            <div>
                <h4 class="text-lg font-semibold mb-4 text-primary">Kontak</h4>
                <ul class="space-y-3">
                    <li class="flex items-start">
                        <svg class="w-5 h-5 text-primary mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        <span class="text-gray-300">Jl. Contoh No. 123, Bekasi Kota, Bekasi</span>
                    </li>
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-primary mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                        </svg>
                        <a href="https://wa.me/6281234567890" class="text-gray-300 hover:text-white transition-colors">+62 812-3456-7890</a>
                    </li>
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-primary mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span class="text-gray-300">Senin - Sabtu: 08:00 - 18:00</span>
                    </li>
                </ul>
            </div>
            
            <!-- Links -->
            <div>
                <h4 class="text-lg font-semibold mb-4 text-primary">Quick Links</h4>
                <ul class="space-y-2">
                    <li><a href="#cara-pesan" class="text-gray-300 hover:text-white transition-colors">Cara Pemesanan</a></li>
                    <li><a href="#testimoni" class="text-gray-300 hover:text-white transition-colors">Testimoni</a></li>
                    <li><a href="#" class="text-gray-300 hover:text-white transition-colors">Kebijakan Privasi</a></li>
                    <li><a href="#" class="text-gray-300 hover:text-white transition-colors">Syarat & Ketentuan</a></li>
                </ul>
            </div>
            
        </div>
        
        <!-- Bottom Bar -->
        <div class="border-t border-gray-800 pt-8">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <p class="text-gray-400 text-sm">
                    &copy; 2024 Noda Bersih Laundry. All rights reserved.
                </p>
                <div class="mt-4 md:mt-0">
                    <a href="#top" class="text-gray-400 hover:text-white text-sm transition-colors opacity-0 invisible transition-all duration-300">
                        Back to Top
                    </a>
                </div>
            </div>
        </div>
    </div>
</footer>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        
        // 1. Mobile Menu Toggle
        const menuButton = document.querySelector('[data-menu-button]');
        const mobileMenu = document.querySelector('[data-mobile-menu]');
        
        if (menuButton && mobileMenu) {
            menuButton.addEventListener('click', function() {
                const isExpanded = menuButton.getAttribute('aria-expanded') === 'true';
                menuButton.setAttribute('aria-expanded', !isExpanded);
                mobileMenu.classList.toggle('hidden');
                
                // Toggle hamburger icon
                const menuIcon = menuButton.querySelector('svg');
                if (menuIcon) {
                    menuIcon.classList.toggle('hidden');
                    menuButton.querySelector('svg:last-child')?.classList.toggle('hidden');
                }
            });
        }
        
        // 2. Smooth Scrolling for Anchor Links
        const scrollLinks = document.querySelectorAll('a[href^="#"]');
        
        scrollLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const targetId = this.getAttribute('href');
                
                if (targetId === '#') return;
                
                const targetElement = document.querySelector(targetId);
                if (targetElement) {
                    targetElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                    
                    // Close mobile menu if open
                    if (mobileMenu && !mobileMenu.classList.contains('hidden')) {
                        mobileMenu.classList.add('hidden');
                        menuButton.setAttribute('aria-expanded', 'false');
                        const menuIcon = menuButton.querySelector('svg');
                        if (menuIcon) {
                            menuButton.querySelector('svg:first-child')?.classList.remove('hidden');
                            menuButton.querySelector('svg:last-child')?.classList.add('hidden');
                        }
                    }
                }
            });
        });
        
        // 3. WhatsApp Button Animation on Scroll
        const whatsappButtons = document.querySelectorAll('[href*="wa.me"]');
        
        whatsappButtons.forEach(button => {
            // Add hover effect enhancement
            button.addEventListener('mouseenter', function() {
                this.classList.add('scale-105');
            });
            
            button.addEventListener('mouseleave', function() {
                this.classList.remove('scale-105');
            });
        });
        
        // 4. Scroll Animation for Elements
        const animateOnScroll = function() {
            const elements = document.querySelectorAll('[data-animate]');
            
            elements.forEach(element => {
                const elementPosition = element.getBoundingClientRect().top;
                const screenPosition = window.innerHeight / 1.3;
                
                if (elementPosition < screenPosition) {
                    element.classList.add('opacity-100', 'translate-y-0');
                    element.classList.remove('opacity-0', 'translate-y-8');
                }
            });
        };
        
        // Initialize scroll animations
        const animatedElements = document.querySelectorAll('[data-animate]');
        animatedElements.forEach(element => {
            element.classList.add('transition-all', 'duration-700', 'ease-out');
            if (!element.classList.contains('opacity-100')) {
                element.classList.add('opacity-0', 'translate-y-8');
            }
        });
        
        // 5. Header Scroll Effect
        const header = document.querySelector('header') || document.querySelector('nav');
        let lastScrollY = window.scrollY;
        
        if (header) {
            window.addEventListener('scroll', function() {
                if (window.scrollY > 100) {
                    header.classList.add('shadow-md', 'py-2');
                    header.classList.remove('py-4');
                } else {
                    header.classList.remove('shadow-md', 'py-2');
                    header.classList.add('py-4');
                }
                
                // Hide header on scroll down, show on scroll up
                if (window.scrollY > lastScrollY && window.scrollY > 200) {
                    header.style.transform = 'translateY(-100%)';
                } else {
                    header.style.transform = 'translateY(0)';
                }
                lastScrollY = window.scrollY;
            });
        }
        
        // 6. Form Validation (if you add contact form later)
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            form.addEventListener('submit', function(e) {
                // Basic validation can be added here
                console.log('Form submitted');
            });
        });
        
        // 7. Performance: Debounce scroll events
        function debounce(func, wait = 20) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
        
        // Attach scroll listeners with debounce
        const debouncedScroll = debounce(animateOnScroll);
        window.addEventListener('scroll', debouncedScroll);
        
        // Initial check for elements in viewport
        animateOnScroll();
        
        // 8. Back to Top Button Functionality
        const backToTopButton = document.querySelector('[href="#top"]');
        if (backToTopButton) {
            window.addEventListener('scroll', function() {
                if (window.scrollY > 300) {
                    backToTopButton.classList.remove('opacity-0', 'invisible');
                    backToTopButton.classList.add('opacity-100', 'visible');
                } else {
                    backToTopButton.classList.add('opacity-0', 'invisible');
                    backToTopButton.classList.remove('opacity-100', 'visible');
                }
            });
        }
        
        // 9. Active Navigation Highlight
        const sections = document.querySelectorAll('section[id]');
        const navLinks = document.querySelectorAll('nav a[href^="#"]');
        
        function highlightNav() {
            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                if (window.scrollY >= sectionTop - 200) {
                    current = section.getAttribute('id');
                }
            });
            
            navLinks.forEach(link => {
                link.classList.remove('text-primary', 'font-semibold');
                if (link.getAttribute('href') === `#${current}`) {
                    link.classList.add('text-primary', 'font-semibold');
                }
            });
        }
        
        window.addEventListener('scroll', debounce(highlightNav));
        
        // 10. WhatsApp Message Pre-fill Enhancement
        const whatsappLinks = document.querySelectorAll('[href*="wa.me"]');
        whatsappLinks.forEach(link => {
            link.addEventListener('click', function() {
                // Analytics tracking (optional)
                console.log('WhatsApp link clicked:', this.href);
            });
        });
    });
    
    // 11. Utility Functions
    const Utils = {
        // Smooth scroll to element
        smoothScrollTo: function(element, offset = 0) {
            const elementPosition = element.getBoundingClientRect().top + window.pageYOffset;
            const offsetPosition = elementPosition - offset;
            
            window.scrollTo({
                top: offsetPosition,
                behavior: 'smooth'
            });
        },
        
        // Check if element is in viewport
        isInViewport: function(element) {
            const rect = element.getBoundingClientRect();
            return (
                rect.top >= 0 &&
                rect.left >= 0 &&
                rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
                rect.right <= (window.innerWidth || document.documentElement.clientWidth)
            );
        }
    };
    
    // 12. Performance Optimization - FIXED VERSION
    if ('loading' in HTMLImageElement.prototype) {
        // Native lazy loading supported
        const images = document.querySelectorAll('img[loading="lazy"][data-src]');
        images.forEach(img => {
            // Only process images that have data-src attribute
            if (img.dataset.src) {
                img.src = img.dataset.src;
            }
        });
    }
    
    // 13. Accessibility Enhancements
    document.addEventListener('keydown', function(e) {
        // ESC key to close mobile menu
        if (e.key === 'Escape') {
            const mobileMenu = document.querySelector('[data-mobile-menu]');
            const menuButton = document.querySelector('[data-menu-button]');
            
            if (mobileMenu && !mobileMenu.classList.contains('hidden')) {
                mobileMenu.classList.add('hidden');
                menuButton?.setAttribute('aria-expanded', 'false');
                menuButton?.focus();
            }
        }
    });
    
    // 14. Window Load Events
    window.addEventListener('load', function() {
        // Remove loading classes
        document.body.classList.remove('loading');
        
        // Trigger initial animations
        setTimeout(() => {
            document.querySelectorAll('[data-animate]').forEach(el => {
                if (Utils.isInViewport(el)) {
                    el.classList.add('opacity-100', 'translate-y-0');
                    el.classList.remove('opacity-0', 'translate-y-8');
                }
            });
        }, 100);
    });
    </script>
</body>
</html>